/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Formik } from 'formik';

import { FormikDate } from './FormikDate';

// Mock the design system components
jest.mock('@ghq-abi/design-system-v2', () => ({
  Container: ({ children, className }: any) => <div className={className}>{children}</div>,
  Label: ({ children, htmlFor, className }: any) => <label htmlFor={htmlFor} className={className}>{children}</label>,
  Input: ({ type, value, onChange, placeholder, min, max, className, ...props }: any) => (
    <input
      type={type}
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      min={min}
      max={max}
      className={className}
      {...props}
    />
  ),
  DateInput: ({ value, onChange, placeholder, className, ...props }: any) => (
    <input
      type="date"
      value={value ? value.toISOString().split('T')[0] : ''}
      onChange={(e) => onChange(e.target.value ? new Date(e.target.value) : null)}
      placeholder={placeholder}
      className={className}
      {...props}
    />
  ),
}));

// Mock the YearPicker component
jest.mock('~/shared/components/YearPicker', () => ({
  YearPicker: ({ value, onChange, placeholder, className, ...props }: any) => (
    <input
      type="text"
      value={value || ''}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      className={className}
      data-testid="year-picker"
      {...props}
    />
  ),
}));

// Mock Formik's ErrorMessage
jest.mock('formik', () => ({
  ...jest.requireActual('formik'),
  ErrorMessage: ({ name, component: Component = 'div', className }: any) => (
    <Component className={className} data-testid={`error-${name}`}>
      Test error message
    </Component>
  ),
}));

const FormikWrapper = ({ children, initialValues = {}, onSubmit = jest.fn() }: any) => (
  <Formik initialValues={initialValues} onSubmit={onSubmit}>
    {children}
  </Formik>
);

describe('FormikDate Component', () => {
  const currentYear = new Date().getFullYear();

  describe('Year-only mode (yearOnly={true})', () => {
    it('should render year picker when yearOnly is true', () => {
      render(
        <FormikWrapper initialValues={{ testDate: '' }}>
          <FormikDate
            name="testDate"
            label="Test Year"
            yearOnly={true}
          />
        </FormikWrapper>
      );

      const input = screen.getByTestId('year-picker');
      expect(input).toBeInTheDocument();
      expect(input).toHaveAttribute('type', 'text');
    });

    it('should pass correct props to year picker', () => {
      render(
        <FormikWrapper initialValues={{ testDate: '' }}>
          <FormikDate
            name="testDate"
            label="Test Year"
            yearOnly={true}
          />
        </FormikWrapper>
      );

      const input = screen.getByTestId('year-picker');
      expect(input).toHaveAttribute('placeholder', 'YYYY');
    });

    it('should display YYYY placeholder when no custom placeholder is provided', () => {
      render(
        <FormikWrapper initialValues={{ testDate: '' }}>
          <FormikDate
            name="testDate"
            label="Test Year"
            yearOnly={true}
          />
        </FormikWrapper>
      );

      const input = screen.getByTestId('year-picker');
      expect(input).toHaveAttribute('placeholder', 'YYYY');
    });

    it('should display custom placeholder when provided', () => {
      render(
        <FormikWrapper initialValues={{ testDate: '' }}>
          <FormikDate
            name="testDate"
            label="Test Year"
            placeholder="Enter year"
            yearOnly={true}
          />
        </FormikWrapper>
      );

      const input = screen.getByTestId('year-picker');
      expect(input).toHaveAttribute('placeholder', 'Enter year');
    });

    it('should extract year from YYYY-01-01 format and display in input', () => {
      render(
        <FormikWrapper initialValues={{ testDate: '2024-01-01' }}>
          <FormikDate
            name="testDate"
            label="Test Year"
            yearOnly={true}
          />
        </FormikWrapper>
      );

      const input = screen.getByTestId('year-picker');
      expect(input).toHaveValue('2024');
    });

    it('should convert year input to YYYY-01-01 format', () => {
      const mockOnChange = jest.fn();

      render(
        <FormikWrapper initialValues={{ testDate: '' }}>
          <FormikDate
            name="testDate"
            label="Test Year"
            onChange={mockOnChange}
            yearOnly={true}
          />
        </FormikWrapper>
      );

      const input = screen.getByTestId('year-picker');
      fireEvent.change(input, { target: { value: '2025' } });

      expect(mockOnChange).toHaveBeenCalledWith({
        target: { value: '2025-01-01' }
      });
    });

    it('should handle empty year input', () => {
      const mockOnChange = jest.fn();

      render(
        <FormikWrapper initialValues={{ testDate: '2024-01-01' }}>
          <FormikDate
            name="testDate"
            label="Test Year"
            onChange={mockOnChange}
            yearOnly={true}
          />
        </FormikWrapper>
      );

      const input = screen.getByTestId('year-picker');
      fireEvent.change(input, { target: { value: '' } });

      expect(mockOnChange).toHaveBeenCalledWith({
        target: { value: '' }
      });
    });
  });

  describe('Regular date mode (yearOnly={false} or undefined)', () => {
    it('should render DateInput when yearOnly is false', () => {
      render(
        <FormikWrapper initialValues={{ testDate: null }}>
          <FormikDate
            name="testDate"
            label="Test Date"
            yearOnly={false}
          />
        </FormikWrapper>
      );

      const input = screen.getByRole('textbox');
      expect(input).toBeInTheDocument();
      expect(input).toHaveAttribute('type', 'date');
    });

    it('should render DateInput when yearOnly is not specified', () => {
      render(
        <FormikWrapper initialValues={{ testDate: null }}>
          <FormikDate
            name="testDate"
            label="Test Date"
          />
        </FormikWrapper>
      );

      const input = screen.getByRole('textbox');
      expect(input).toBeInTheDocument();
      expect(input).toHaveAttribute('type', 'date');
    });

    it('should handle date changes in regular mode', () => {
      const mockOnChange = jest.fn();

      render(
        <FormikWrapper initialValues={{ testDate: null }}>
          <FormikDate
            name="testDate"
            label="Test Date"
            onChange={mockOnChange}
          />
        </FormikWrapper>
      );

      const input = screen.getByRole('textbox');
      fireEvent.change(input, { target: { value: '2024-06-15' } });

      expect(mockOnChange).toHaveBeenCalled();
    });
  });

  describe('Utility functions', () => {
    it('should calculate correct year range', () => {
      const yearRange = Array.from({ length: 21 }, (_, i) => currentYear - 10 + i);

      expect(yearRange).toHaveLength(21);
      expect(yearRange[0]).toBe(currentYear - 10);
      expect(yearRange[20]).toBe(currentYear + 10);
    });

    it('should format date correctly for backend', () => {
      const selectedYear = '2024';
      const expectedDateValue = `${selectedYear}-01-01`;

      expect(expectedDateValue).toBe('2024-01-01');
    });

    it('should extract year from date string correctly', () => {
      const dateValue = '2024-01-01';
      const extractedYear = dateValue.split('-')[0];

      expect(extractedYear).toBe('2024');
    });
  });

  describe('Props and integration', () => {
    it('should pass through additional props to year picker in year-only mode', () => {
      render(
        <FormikWrapper initialValues={{ testDate: '' }}>
          <FormikDate
            name="testDate"
            label="Test Year"
            yearOnly={true}
            disabled={true}
          />
        </FormikWrapper>
      );

      const input = screen.getByTestId('year-picker');
      expect(input).toBeDisabled();
    });

    it('should render error message container', () => {
      render(
        <FormikWrapper initialValues={{ testDate: '' }}>
          <FormikDate
            name="testDate"
            label="Test Year"
            yearOnly={true}
          />
        </FormikWrapper>
      );

      const errorContainer = screen.getByTestId('error-testDate');
      expect(errorContainer).toBeInTheDocument();
    });

    it('should render label correctly', () => {
      render(
        <FormikWrapper initialValues={{ testDate: '' }}>
          <FormikDate
            name="testDate"
            label="Test Year Label"
            yearOnly={true}
          />
        </FormikWrapper>
      );

      const label = screen.getByText('Test Year Label');
      expect(label).toBeInTheDocument();
    });
  });
});
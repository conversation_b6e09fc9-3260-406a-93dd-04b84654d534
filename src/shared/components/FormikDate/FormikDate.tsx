import React from 'react';
import { Container, DateInput, Label } from '@ghq-abi/design-system-v2';
import { ErrorMessage, useField } from 'formik';

import { cn } from '~/shared/utils/cn';
import { YearPicker } from '~/shared/components/YearPicker';

import { FormikDateProps } from './types';

export function FormikDate({
  name,
  label,
  placeholder,
  onChange,
  value,
  yearOnly = false,
  ...props
}: FormikDateProps) {
  const [field, meta, helpers] = useField(name);
  const hasError = meta.touched && meta.error;

  const currentYear = new Date().getFullYear();

  // For year-only mode, extract year from YYYY-01-01 format or use empty string
  const yearValue = yearOnly && field.value ? field.value.split('-')[0] : '';
  const dateValue = !yearOnly && field.value ? new Date(field.value) : null;

  const handleDateChange = (date: Date | null) => {
    const dateValue = date ? date.toISOString().split('T')[0] : '';
    void helpers.setValue(dateValue);
    if (onChange) {
      onChange({ target: { value: dateValue } } as any);
    }
  };

  const handleYearChange = (year: string) => {
    const dateValue = year ? `${year}-01-01` : '';
    void helpers.setValue(dateValue);
    if (onChange) {
      onChange({ target: { value: dateValue } } as any);
    }
  };

  return (
    <div className="grid w-full items-center">
      <Label className="pb-1" htmlFor={name}>
        {label}
      </Label>
      {yearOnly ? (
        <YearPicker
          value={yearValue}
          onChange={handleYearChange}
          placeholder={placeholder || 'YYYY'}
          minYear={currentYear - 10}
          maxYear={currentYear + 10}
          className={cn(
            'bg-gray-50 block text-neutral-500 border-neutral-200',
            hasError ? 'border-red-500 focus:border-red-500' : '',
          )}
          {...props}
        />
      ) : (
        <DateInput
          value={dateValue}
          onChange={handleDateChange}
          placeholder={placeholder}
          className={cn(
            'bg-gray-50 block text-neutral-500 border-neutral-200',
            hasError ? 'border-red-500 focus:border-red-500' : '',
          )}
          {...props}
        />
      )}
      <Container className="min-h-[20px]">
        <ErrorMessage
          name={name}
          component="div"
          className="text-red-500 text-sm"
        />
      </Container>
    </div>
  );
}

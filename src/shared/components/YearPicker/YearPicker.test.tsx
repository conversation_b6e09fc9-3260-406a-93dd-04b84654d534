/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';

import { YearPicker } from './YearPicker';

// Mock the design system components
jest.mock('@ghq-abi/design-system-v2', () => ({
  Input: React.forwardRef(({ type, value, onChange, placeholder, className, onClick, readOnly, disabled, ...props }: any, ref: any) => (
    <input
      ref={ref}
      type={type}
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      className={className}
      onClick={onClick}
      readOnly={readOnly}
      disabled={disabled}
      {...props}
    />
  )),
}));

// Mock react-bootstrap-icons
jest.mock('react-bootstrap-icons', () => ({
  ChevronDown: () => <div data-testid="chevron-down">ChevronDown</div>,
  ChevronLeft: () => <div data-testid="chevron-left">ChevronLeft</div>,
  ChevronRight: () => <div data-testid="chevron-right">ChevronRight</div>,
}));

describe('YearPicker Component', () => {
  const currentYear = new Date().getFullYear();
  const mockOnChange = jest.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  it('should render input with correct placeholder', () => {
    render(
      <YearPicker
        value=""
        onChange={mockOnChange}
        placeholder="Select Year"
      />
    );

    const input = screen.getByPlaceholderText('Select Year');
    expect(input).toBeInTheDocument();
    expect(input).toHaveAttribute('type', 'text');
    expect(input).toHaveAttribute('readonly');
  });

  it('should display selected year value', () => {
    render(
      <YearPicker
        value="2024"
        onChange={mockOnChange}
      />
    );

    const input = screen.getByDisplayValue('2024');
    expect(input).toBeInTheDocument();
  });

  it('should open dropdown when input is clicked', () => {
    render(
      <YearPicker
        value=""
        onChange={mockOnChange}
      />
    );

    const input = screen.getByRole('textbox');
    fireEvent.click(input);

    // Check if decade navigation is visible (indicates dropdown is open)
    expect(screen.getByTestId('chevron-left')).toBeInTheDocument();
    expect(screen.getByTestId('chevron-right')).toBeInTheDocument();
  });

  it('should display current decade range', () => {
    render(
      <YearPicker
        value=""
        onChange={mockOnChange}
      />
    );

    const input = screen.getByRole('textbox');
    fireEvent.click(input);

    const currentDecade = Math.floor(currentYear / 10) * 10;
    const decadeText = `${currentDecade} - ${currentDecade + 9}`;
    expect(screen.getByText(decadeText)).toBeInTheDocument();
  });

  it('should call onChange when year is selected', () => {
    render(
      <YearPicker
        value=""
        onChange={mockOnChange}
      />
    );

    const input = screen.getByRole('textbox');
    fireEvent.click(input);

    // Click on current year button
    const yearButton = screen.getByText(currentYear.toString());
    fireEvent.click(yearButton);

    expect(mockOnChange).toHaveBeenCalledWith(currentYear.toString());
  });

  it('should highlight current year when not selected', () => {
    render(
      <YearPicker
        value=""
        onChange={mockOnChange}
      />
    );

    const input = screen.getByRole('textbox');
    fireEvent.click(input);

    const currentYearButton = screen.getByText(currentYear.toString());
    expect(currentYearButton).toHaveClass('font-semibold');
  });

  it('should respect min and max year constraints', () => {
    const minYear = currentYear - 5;
    const maxYear = currentYear + 5;

    render(
      <YearPicker
        value=""
        onChange={mockOnChange}
        minYear={minYear}
        maxYear={maxYear}
      />
    );

    const input = screen.getByRole('textbox');
    fireEvent.click(input);

    // Should not show years outside the range
    const tooEarlyYear = minYear - 1;
    const tooLateYear = maxYear + 1;

    expect(screen.queryByText(tooEarlyYear.toString())).not.toBeInTheDocument();
    expect(screen.queryByText(tooLateYear.toString())).not.toBeInTheDocument();

    // Should show years within the range
    expect(screen.getByText(minYear.toString())).toBeInTheDocument();
    expect(screen.getByText(maxYear.toString())).toBeInTheDocument();
  });

  it('should be disabled when disabled prop is true', () => {
    render(
      <YearPicker
        value=""
        onChange={mockOnChange}
        disabled={true}
      />
    );

    const input = screen.getByRole('textbox');
    expect(input).toBeDisabled();

    // Should not open dropdown when disabled
    fireEvent.click(input);
    expect(screen.queryByTestId('chevron-left')).not.toBeInTheDocument();
  });
});

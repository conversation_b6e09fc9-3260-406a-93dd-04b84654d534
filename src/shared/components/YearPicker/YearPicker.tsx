import React, { useState, useRef, useEffect } from 'react';
import { Input } from '@ghq-abi/design-system-v2';
import { ChevronDown, ChevronLeft, ChevronRight } from 'react-bootstrap-icons';

import { cn } from '~/shared/utils/cn';

interface YearPickerProps {
  /** Current selected year value */
  value?: string;
  /** Callback function called when a year is selected */
  onChange: (year: string) => void;
  /** Placeholder text for the input */
  placeholder?: string;
  /** Additional CSS classes */
  className?: string;
  /** Whether the picker is disabled */
  disabled?: boolean;
  /** Minimum selectable year (defaults to current year - 10) */
  minYear?: number;
  /** Maximum selectable year (defaults to current year + 10) */
  maxYear?: number;
  /** Additional props passed to the input */
  [key: string]: any;
}

/**
 * YearPicker component provides a visual calendar-style interface for year selection.
 * Features a dropdown with decade navigation and a grid of selectable years.
 * Integrates seamlessly with form libraries and maintains consistent styling with the design system.
 */
export function YearPicker({
  value,
  onChange,
  placeholder = 'YYYY',
  className,
  disabled = false,
  minYear,
  maxYear,
  ...props
}: YearPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentDecade, setCurrentDecade] = useState(() => {
    const currentYear = new Date().getFullYear();
    const selectedYear = value ? parseInt(value, 10) : currentYear;
    return Math.floor(selectedYear / 10) * 10;
  });
  
  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const currentYear = new Date().getFullYear();
  const effectiveMinYear = minYear ?? currentYear - 10;
  const effectiveMaxYear = maxYear ?? currentYear + 10;

  // Generate years for current decade view
  const generateYears = () => {
    const years = [];
    for (let i = 0; i < 12; i++) {
      const year = currentDecade + i;
      if (year >= effectiveMinYear && year <= effectiveMaxYear) {
        years.push(year);
      }
    }
    return years;
  };

  const years = generateYears();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleInputClick = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const handleYearSelect = (year: number) => {
    onChange(year.toString());
    setIsOpen(false);
    inputRef.current?.focus();
  };

  const handlePreviousDecade = () => {
    setCurrentDecade(prev => Math.max(prev - 10, Math.floor(effectiveMinYear / 10) * 10));
  };

  const handleNextDecade = () => {
    setCurrentDecade(prev => Math.min(prev + 10, Math.floor(effectiveMaxYear / 10) * 10));
  };

  const canGoPrevious = currentDecade > Math.floor(effectiveMinYear / 10) * 10;
  const canGoNext = currentDecade < Math.floor(effectiveMaxYear / 10) * 10;

  return (
    <div ref={containerRef} className="relative">
      <div className="relative">
        <Input
          ref={inputRef}
          type="text"
          value={value || ''}
          placeholder={placeholder}
          className={cn(
            'cursor-pointer pr-8',
            className
          )}
          onClick={handleInputClick}
          readOnly
          disabled={disabled}
          {...props}
        />
        <ChevronDown
          className={cn(
            'absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-500 pointer-events-none transition-transform duration-200',
            isOpen && 'rotate-180'
          )}
        />
      </div>

      {isOpen && (
        <div className="absolute z-50 mt-1 w-full bg-white border rounded-md shadow-lg" style={{ borderColor: '#CACDD5', boxShadow: '8px 0px 16px 0px #191F2E3D' }}>
          <div className="p-3">
            {/* Decade navigation */}
            <div className="flex items-center justify-between mb-3">
              <button
                type="button"
                onClick={handlePreviousDecade}
                disabled={!canGoPrevious}
                className={cn(
                  'p-2 rounded-md hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1',
                  !canGoPrevious && 'opacity-50 cursor-not-allowed hover:bg-transparent'
                )}
              >
                <ChevronLeft className="w-4 h-4 text-gray-600" />
              </button>
              <span className="text-sm font-medium text-gray-700 px-2">
                {currentDecade} - {currentDecade + 9}
              </span>
              <button
                type="button"
                onClick={handleNextDecade}
                disabled={!canGoNext}
                className={cn(
                  'p-2 rounded-md hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1',
                  !canGoNext && 'opacity-50 cursor-not-allowed hover:bg-transparent'
                )}
              >
                <ChevronRight className="w-4 h-4 text-gray-600" />
              </button>
            </div>

            {/* Years grid */}
            <div className="grid grid-cols-3 gap-2">
              {years.map((year) => (
                <button
                  key={year}
                  type="button"
                  onClick={() => handleYearSelect(year)}
                  className={cn(
                    'px-3 py-2 text-sm rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1',
                    value === year.toString()
                      ? 'bg-blue-500 text-white hover:bg-blue-600 font-medium'
                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900',
                    year === currentYear && value !== year.toString() && 'font-semibold bg-gray-50'
                  )}
                >
                  {year}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
